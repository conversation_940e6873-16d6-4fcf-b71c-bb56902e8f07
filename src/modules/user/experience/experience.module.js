/**
 * Experience Module
 *
 * Defines experience routes and their handlers
 */
const express = require('express');
const router = express.Router();

// Import middleware
const { authenticate } = require('@middlewares/auth.middleware');
const { requireProviderPlus } = require('@middlewares/role.middleware');
const { validate } = require('@middlewares/validation.middleware');

// Import controller and validation
const experienceController = require('./experience.controller');
const experienceValidation = require('./experience.validation');
const paginationMiddleware = require('@middlewares/pagination.middleware');

/**
 * Experience routes
 */

// POST /user/experiences - Create a new experience
router.post(
  '/',
  authenticate,
  requireProviderPlus,
  validate(experienceValidation.create),
  experienceController.createExperience
);

// GET /user/experiences - Get all experiences with pagination and search
router.get(
  '/',
  authenticate,
  paginationMiddleware,
  validate(experienceValidation.getAll),
  experienceController.getAllExperiences
);

// GET /user/experiences/:experienceId - Get experience by ID
router.get(
  '/:experienceId',
  authenticate,
  validate(experienceValidation.getById),
  experienceController.getExperienceById
);

// POST /user/experiences/:experienceId/enroll - Enroll in an experience
router.post(
  '/:experienceId/enroll',
  authenticate,
  validate(experienceValidation.enrollUser),
  experienceController.enrollUser
);

// GET /user/experiences/enrollments - Get user's enrollments
router.get(
  '/enrollments',
  authenticate,
  paginationMiddleware,
  validate(experienceValidation.getUserEnrollments),
  experienceController.getUserEnrollments
);

// PATCH /user/experiences/enrollments/:enrollmentId/status - Update enrollment status
router.patch(
  '/enrollments/:enrollmentId/status',
  authenticate,
  validate(experienceValidation.updateEnrollmentStatus),
  experienceController.updateEnrollmentStatus
);

module.exports = router;
