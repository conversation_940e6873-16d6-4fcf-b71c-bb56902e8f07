/**
 * Application Messages
 *
 * Contains all application-wide messages for consistency
 * This includes error messages, success messages, and other static text
 */

/**
 * Common messages used across the application
 */
const COMMON = {
  // Success messages
  SUCCESS: 'Operation completed successfully',
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',

  // Error messages
  INTERNAL_ERROR: 'Internal server error',
  NOT_FOUND: 'Resource not found',
  INVALID_REQUEST: 'Invalid request',
  VALIDATION_ERROR: 'Validation error',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Forbidden access',
  CONFLICT: 'Resource already exists',
};

/**
 * Authentication related messages
 */
const AUTH = {
  // Success messages
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  REGISTER_SUCCESS: 'Registration successful',
  PASSWORD_RESET_SUCCESS: 'Password reset successful',
  PASSWORD_RESET_EMAIL_SENT: 'Password reset email sent',
  PROFILE_RETRIEVED: 'Profile retrieved successfully',

  // Error messages
  INVALID_CREDENTIALS: 'Invalid credentials',
  ACCOUNT_LOCKED: 'Account is locked',
  ACCOUNT_DISABLED: 'Account is disabled',
  EMAIL_ALREADY_EXISTS: 'Email already registered',
  INVALID_TOKEN: 'Invalid token',
  TOKEN_EXPIRED: 'Token has expired',
  PASSWORD_MISMATCH: 'Passwords do not match',
  PASSWORD_SAME: 'New password must be different from old password',
  PASSWORD_WEAK: 'Password does not meet security requirements',
  EMAIL_NOT_FOUND: 'Email not registered',
  AUTHENTICATION_FAILED: 'Authentication failed',
  UNAUTHORIZED: 'You do not have the required role to access this resource',
};

/**
 * Admin related messages
 */
const ADMIN = {
  // Success messages
  LOGIN_SUCCESS: 'Admin login successful',
  PROFILE_RETRIEVED: 'Admin profile retrieved successfully',
  ADMIN_CREATED: 'Admin created successfully',
  ADMIN_UPDATED: 'Admin updated successfully',
  ADMIN_DELETED: 'Admin deleted successfully',

  // Error messages
  ADMIN_NOT_FOUND: 'Admin not found',
  ADMIN_EXISTS: 'Admin already exists',
};

/**
 * User related messages
 */
const USER = {
  // Success messages
  PROFILE_UPDATED: 'Profile updated successfully',
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',

  // Error messages
  USER_NOT_FOUND: 'User not found',
  USER_EXISTS: 'User already exists',
};

/**
 * Validation related messages
 */
const VALIDATION = {
  REQUIRED: 'This field is required',
  EMAIL_INVALID: 'Valid email is required',
  PASSWORD_LENGTH: 'Password must be at least 6 characters long',
  NAME_REQUIRED: 'Name is required',
  FIRST_NAME_REQUIRED: 'First name is required',
  LAST_NAME_REQUIRED: 'Last name is required',
  INVALID_FORMAT: 'Invalid format',
  INVALID_DATE: 'Invalid date format',
  INVALID_PHONE: 'Invalid phone number',
  INVALID_URL: 'Invalid URL format',
};

/**
 * File upload related messages
 */
const UPLOAD = {
  // Success messages
  UPLOAD_SUCCESS: 'File uploaded successfully',

  // Error messages
  UPLOAD_FAILED: 'File upload failed',
  INVALID_FILE_TYPE: 'Invalid file type',
  FILE_TOO_LARGE: 'File size exceeds limit',
  FILE_NOT_FOUND: 'File not found',
};

/**
 * PD Category related messages
 */
const PD_CATEGORY = {
  // Success messages
  CREATED: 'PD category created successfully',
  UPDATED: 'PD category updated successfully',
  DELETED: 'PD category deleted successfully',
  RETRIEVED: 'PD category retrieved successfully',
  ALL_RETRIEVED: 'All PD categories retrieved successfully',

  // Error messages
  NOT_FOUND: 'PD category not found',
  ALREADY_EXISTS: 'PD category with this name already exists',
};

/**
 * Focus related messages
 */
const FOCUS = {
  // Success messages
  CREATED: 'Focus created successfully',
  UPDATED: 'Focus updated successfully',
  DELETED: 'Focus deleted successfully',
  RETRIEVED: 'Focus retrieved successfully',
  ALL_RETRIEVED: 'All focus areas retrieved successfully',

  // Error messages
  NOT_FOUND: 'Focus not found',
  ALREADY_EXISTS: 'Focus with this name already exists',
};

/**
 * WTD Category related messages
 */
const WTD_CATEGORY = {
  // Success messages
  CREATED: 'WTD category created successfully',
  UPDATED: 'WTD category updated successfully',
  DELETED: 'WTD category deleted successfully',
  RETRIEVED: 'WTD category retrieved successfully',
  ALL_RETRIEVED: 'All WTD categories retrieved successfully',

  // Error messages
  NOT_FOUND: 'WTD category not found',
  ALREADY_EXISTS: 'WTD category with this name already exists',
};

/**
 * Insight related messages
 */
const INSIGHT = {
  // Success messages
  CREATED: 'Insight created successfully',
  UPDATED: 'Insight updated successfully',
  DELETED: 'Insight deleted successfully',
  RETRIEVED: 'Insight retrieved successfully',
  ALL_RETRIEVED: 'All insights retrieved successfully',
  TRENDING_RETRIEVED: 'Trending insights retrieved successfully',
  APPROVED: 'Insight approved successfully',
  REJECTED: 'Insight rejected successfully',
  PENDING: 'Insight is pending review',
  BOOKMARK_ADDED: 'Insight bookmarked successfully',
  BOOKMARK_REMOVED: 'Insight bookmark removed successfully',
  BOOKMARKS_RETRIEVED: 'Bookmarked insights retrieved successfully',
  LIKE_ADDED: 'Insight liked successfully',
  LIKE_REMOVED: 'Insight like removed successfully',
  LIKES_RETRIEVED: 'Liked insights retrieved successfully',
  IMPLEMENT_ADDED: 'Insight marked as implemented successfully',
  IMPLEMENT_REMOVED: 'Insight implementation removed successfully',
  IMPLEMENTS_RETRIEVED: 'Implemented insights retrieved successfully',

  // Error messages
  NOT_FOUND: 'Insight not found',
  UNAUTHORIZED: 'Only provider plus users can manage insights',
  ALREADY_REVIEWED: 'Insight has already been reviewed',
  INVALID_STATUS: 'Invalid status value',
  REJECTION_REASON_REQUIRED: 'Rejection reason is required',
  CANNOT_EDIT_APPROVED: 'Cannot edit an approved insight',
  CANNOT_EDIT_REJECTED: 'Cannot edit a rejected insight',
  CANNOT_DELETE_APPROVED: 'Cannot delete an approved insight',
  CANNOT_DELETE_REJECTED: 'Cannot delete a rejected insight',
};

/**
 * Experience related messages
 */
const EXPERIENCE = {
  // Success messages
  CREATED: 'Experience created successfully',
  UPDATED: 'Experience updated successfully',
  DELETED: 'Experience deleted successfully',
  RETRIEVED: 'Experience retrieved successfully',
  ALL_RETRIEVED: 'All experiences retrieved successfully',
  WEEK_ADDED: 'Week added to experience successfully',
  WEEK_UPDATED: 'Experience week updated successfully',
  WEEK_DELETED: 'Experience week deleted successfully',
  INSIGHT_ADDED: 'Insight added to week successfully',
  INSIGHT_UPDATED: 'Experience insight updated successfully',
  INSIGHT_DELETED: 'Experience insight deleted successfully',
  MEDIA_ADDED: 'Media added successfully',
  MEDIA_UPDATED: 'Media updated successfully',
  MEDIA_DELETED: 'Media deleted successfully',

  // Error messages
  NOT_FOUND: 'Experience not found',
  WEEK_NOT_FOUND: 'Experience week not found',
  INSIGHT_NOT_FOUND: 'Experience insight not found',
  MEDIA_NOT_FOUND: 'Media not found',
  UNAUTHORIZED: 'Only the creator can manage this experience',
  INVALID_WEEK_NUMBER: 'Invalid week number for this experience',
  MAX_INSIGHTS_REACHED: 'Maximum 5 insights allowed per week',
  WEEK_NUMBER_EXISTS: 'Week number already exists for this experience',
  INVALID_PD_CATEGORY_IDS: 'Invalid PD Category IDs: %s',
  INVALID_WTD_CATEGORY_IDS: 'Invalid WTD Category IDs: %s',
  INVALID_FOCUS_IDS: 'Invalid Focus IDs: %s',
  INVALID_PD_CATEGORY_IDS_INSIGHT: 'Invalid PD Category IDs in insight: %s',
  INVALID_WTD_CATEGORY_IDS_INSIGHT: 'Invalid WTD Category IDs in insight: %s',
};

/**
 * Report messages
 */
const REPORT = {
  // Success messages
  INSIGHT_REPORTED: 'Insight reported successfully',
  CONTRIBUTION_REPORTED: 'Contribution reported successfully',
  REPORT_UPDATED: 'Report status updated successfully',
  REPORTS_RETRIEVED: 'Reports retrieved successfully',
  REPORT_RETRIEVED: 'Report retrieved successfully',
  REPORT_STATISTICS_RETRIEVED: 'Report statistics retrieved successfully',

  // Error messages
  REPORT_NOT_FOUND: 'Report not found',
  ALREADY_REPORTED: 'You have already reported this content',
  CANNOT_REPORT_OWN_CONTENT: 'You cannot report your own content',
  INVALID_REPORT_STATUS: 'Invalid report status',
  INSIGHT_NOT_FOUND: 'Insight not found',
  CONTRIBUTION_NOT_FOUND: 'Contribution not found',
};

// Experience enrollment messages
const EXPERIENCE_ENROLLMENT_CREATED = 'User enrolled successfully';
const EXPERIENCE_ENROLLMENT_UPDATED = 'Enrollment status updated successfully';
const EXPERIENCE_ENROLLMENT_NOT_FOUND = 'Enrollment not found';
const USER_ALREADY_ENROLLED = 'User is already enrolled in this experience';
const INVALID_START_DATE = 'Start date must be a Monday';
const INVALID_ENROLLMENT_STATUS =
  'Status must be either REGISTERED or COMPLETED';

module.exports = {
  COMMON,
  AUTH,
  ADMIN,
  USER,
  VALIDATION,
  UPLOAD,
  PD_CATEGORY,
  FOCUS,
  WTD_CATEGORY,
  INSIGHT,
  EXPERIENCE,
  REPORT,
  EXPERIENCE_ENROLLMENT_CREATED,
  EXPERIENCE_ENROLLMENT_UPDATED,
  EXPERIENCE_ENROLLMENT_NOT_FOUND,
  USER_ALREADY_ENROLLED,
  INVALID_START_DATE,
  INVALID_ENROLLMENT_STATUS,
};
